-- =====================================================
-- Mimi ni Maji - Water Management App Database Schema
-- =====================================================
-- "Mimi ni Maji" means "I am Water" in Swahili
-- Complete database setup for professional water management
-- =====================================================

-- 1. USER PROFILES TABLE
-- Extends Supabase auth.users with additional profile information
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT NOT NULL,
  purpose TEXT NOT NULL CHECK (purpose IN ('student', 'customer', 'user')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_sign_in TIMESTAMP WITH TIME ZONE,
  avatar_url TEXT,
  phone TEXT,
  organization TEXT,
  is_active BOOLEAN DEFAULT true
);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for user_profiles
CREATE POLICY "Users can view own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- 2. WATER SOURCES TABLE
-- Track different water sources (wells, reservoirs, municipal supply, etc.)
CREATE TABLE water_sources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('well', 'reservoir', 'municipal', 'borehole', 'river', 'lake', 'other')),
  location TEXT,
  capacity_liters DECIMAL(12,2),
  current_level_liters DECIMAL(12,2),
  quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE water_sources ENABLE ROW LEVEL SECURITY;

-- Policies for water_sources
CREATE POLICY "Users can manage own water sources" ON water_sources
  FOR ALL USING (auth.uid() = user_id);

-- 3. WATER USAGE RECORDS TABLE
-- Daily water usage tracking and monitoring
CREATE TABLE water_usage_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  source_id UUID REFERENCES water_sources(id) ON DELETE CASCADE,
  usage_date DATE NOT NULL,
  amount_liters DECIMAL(10,2) NOT NULL,
  usage_type TEXT NOT NULL CHECK (usage_type IN ('domestic', 'irrigation', 'industrial', 'livestock', 'other')),
  cost_amount DECIMAL(10,2),
  currency TEXT DEFAULT 'USD',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE water_usage_records ENABLE ROW LEVEL SECURITY;

-- Policies for water_usage_records
CREATE POLICY "Users can manage own usage records" ON water_usage_records
  FOR ALL USING (auth.uid() = user_id);

-- Indexes for better performance
CREATE INDEX idx_water_usage_user_date ON water_usage_records(user_id, usage_date);
CREATE INDEX idx_water_usage_source ON water_usage_records(source_id);

-- 4. WATER QUALITY TESTS TABLE
-- Track water quality test results and compliance
CREATE TABLE water_quality_tests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  source_id UUID REFERENCES water_sources(id) ON DELETE CASCADE,
  test_date DATE NOT NULL,
  ph_level DECIMAL(3,2),
  tds_ppm INTEGER, -- Total Dissolved Solids in parts per million
  turbidity_ntu DECIMAL(6,2), -- Nephelometric Turbidity Units
  chlorine_mg_l DECIMAL(5,2), -- Chlorine in mg/L
  bacteria_count INTEGER,
  overall_quality TEXT CHECK (overall_quality IN ('excellent', 'good', 'fair', 'poor', 'unsafe')),
  tested_by TEXT,
  lab_name TEXT,
  certificate_url TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE water_quality_tests ENABLE ROW LEVEL SECURITY;

-- Policies for water_quality_tests
CREATE POLICY "Users can manage own quality tests" ON water_quality_tests
  FOR ALL USING (auth.uid() = user_id);

-- 5. WATER EXPENSES TABLE
-- Track water-related expenses and bills
CREATE TABLE water_expenses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  source_id UUID REFERENCES water_sources(id) ON DELETE SET NULL,
  expense_date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  expense_type TEXT NOT NULL CHECK (expense_type IN ('utility_bill', 'maintenance', 'equipment', 'testing', 'treatment', 'other')),
  description TEXT,
  vendor_name TEXT,
  receipt_url TEXT,
  is_recurring BOOLEAN DEFAULT false,
  recurring_frequency TEXT CHECK (recurring_frequency IN ('monthly', 'quarterly', 'yearly')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE water_expenses ENABLE ROW LEVEL SECURITY;

-- Policies for water_expenses
CREATE POLICY "Users can manage own expenses" ON water_expenses
  FOR ALL USING (auth.uid() = user_id);

-- 5. MAINTENANCE SCHEDULES TABLE
-- Track maintenance activities for water sources
CREATE TABLE maintenance_schedules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  source_id UUID REFERENCES water_sources(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  maintenance_type TEXT NOT NULL CHECK (maintenance_type IN ('inspection', 'cleaning', 'repair', 'upgrade', 'other')),
  scheduled_date DATE NOT NULL,
  completed_date DATE,
  status TEXT NOT NULL CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')) DEFAULT 'scheduled',
  description TEXT,
  cost_estimate DECIMAL(10,2),
  actual_cost DECIMAL(10,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE maintenance_schedules ENABLE ROW LEVEL SECURITY;

-- Policies for maintenance_schedules
CREATE POLICY "Users can manage own maintenance schedules" ON maintenance_schedules
  FOR ALL USING (auth.uid() = user_id);

-- 6. WATER BILLS TABLE
-- Track water utility bills and payments
CREATE TABLE water_bills (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  bill_date DATE NOT NULL,
  due_date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  status TEXT NOT NULL CHECK (status IN ('pending', 'paid', 'overdue', 'disputed')) DEFAULT 'pending',
  payment_date DATE,
  payment_reference TEXT,
  bill_period_start DATE NOT NULL,
  bill_period_end DATE NOT NULL,
  consumption_liters DECIMAL(12,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE water_bills ENABLE ROW LEVEL SECURITY;

-- Policies for water_bills
CREATE POLICY "Users can manage own water bills" ON water_bills
  FOR ALL USING (auth.uid() = user_id);

-- 7. ALERTS AND NOTIFICATIONS TABLE
-- Track system alerts and notifications
CREATE TABLE alerts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  source_id UUID REFERENCES water_sources(id) ON DELETE CASCADE,
  alert_type TEXT NOT NULL CHECK (alert_type IN ('low_level', 'quality_issue', 'maintenance_due', 'bill_due', 'usage_anomaly', 'other')),
  severity TEXT NOT NULL CHECK (severity IN ('info', 'warning', 'critical')),
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT false,
  is_resolved BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  resolved_at TIMESTAMP WITH TIME ZONE
);

-- Enable RLS
ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;

-- Policies for alerts
CREATE POLICY "Users can manage own alerts" ON alerts
  FOR ALL USING (auth.uid() = user_id);

-- 8. WATER CONSUMPTION GOALS TABLE
-- Track water conservation goals and targets
CREATE TABLE consumption_goals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  source_id UUID REFERENCES water_sources(id) ON DELETE CASCADE,
  target_period TEXT NOT NULL CHECK (target_period IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
  target_consumption_liters DECIMAL(12,2) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  actual_consumption_liters DECIMAL(12,2),
  status TEXT NOT NULL CHECK (status IN ('active', 'achieved', 'missed', 'cancelled')) DEFAULT 'active',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE consumption_goals ENABLE ROW LEVEL SECURITY;

-- Policies for consumption_goals
CREATE POLICY "Users can manage own consumption goals" ON consumption_goals
  FOR ALL USING (auth.uid() = user_id);

-- 9. USER SETTINGS TABLE
-- Store user preferences and application settings
CREATE TABLE user_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  language TEXT DEFAULT 'en',
  notifications_enabled BOOLEAN DEFAULT true,
  email_notifications BOOLEAN DEFAULT true,
  push_notifications BOOLEAN DEFAULT true,
  sms_notifications BOOLEAN DEFAULT false,
  measurement_unit TEXT DEFAULT 'liters' CHECK (measurement_unit IN ('liters', 'gallons')),
  currency TEXT DEFAULT 'USD',
  theme TEXT DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'system')),
  report_frequency TEXT DEFAULT 'monthly' CHECK (report_frequency IN ('daily', 'weekly', 'monthly', 'quarterly')),
  dashboard_widgets JSONB DEFAULT '[]',
  reminder_time TIME DEFAULT '09:00:00',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- Policies for user_settings
CREATE POLICY "Users can view own settings" ON user_settings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON user_settings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own settings" ON user_settings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create index for better performance
CREATE INDEX idx_user_settings ON user_settings(user_id);

-- Create indexes for better performance
CREATE INDEX idx_maintenance_source ON maintenance_schedules(source_id);
CREATE INDEX idx_bills_user_date ON water_bills(user_id, bill_date);
CREATE INDEX idx_alerts_user ON alerts(user_id);
CREATE INDEX idx_alerts_source ON alerts(source_id);
CREATE INDEX idx_goals_user ON consumption_goals(user_id);
CREATE INDEX idx_goals_source ON consumption_goals(source_id);

-- =====================================================
-- UTILITY FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to relevant tables
CREATE TRIGGER update_water_sources_updated_at 
    BEFORE UPDATE ON water_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_settings_updated_at 
    BEFORE UPDATE ON user_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- USEFUL VIEWS FOR ANALYTICS
-- =====================================================

-- Monthly usage summary view
CREATE VIEW monthly_usage_summary AS
SELECT 
    user_id,
    DATE_TRUNC('month', usage_date) as month,
    SUM(amount_liters) as total_usage,
    SUM(cost_amount) as total_cost,
    COUNT(*) as record_count
FROM water_usage_records
GROUP BY user_id, DATE_TRUNC('month', usage_date);

-- Water source efficiency view
CREATE VIEW source_efficiency AS
SELECT 
    ws.id,
    ws.name,
    ws.user_id,
    COALESCE(SUM(wur.amount_liters), 0) as total_usage,
    ws.capacity_liters,
    CASE 
        WHEN ws.capacity_liters > 0 
        THEN (COALESCE(SUM(wur.amount_liters), 0) / ws.capacity_liters * 100)
        ELSE 0 
    END as usage_percentage
FROM water_sources ws
LEFT JOIN water_usage_records wur ON ws.id = wur.source_id
GROUP BY ws.id, ws.name, ws.user_id, ws.capacity_liters;

-- =====================================================
-- SETUP COMPLETE
-- =====================================================
-- Your Mimi ni Maji database is now ready!
-- Remember to update your Supabase URL and keys in the app
-- =====================================================
