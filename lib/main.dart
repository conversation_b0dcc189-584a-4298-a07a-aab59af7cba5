import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await Supabase.initialize(
    url:
        'https://ghgjqopbvksszylsaifh.supabase.co', // Replace with your Supabase URL
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdoZ2pxb3Bidmtzc3p5bHNhaWZoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0NDExNTUsImV4cCI6MjA2NDAxNzE1NX0.905Tn9q5Mrx72ZCsbjNFTsXyJUqsQfIdZeDazQn3Ric', // Replace with your Supabase anon key
  );

  runApp(const WaterManagementApp());
}
