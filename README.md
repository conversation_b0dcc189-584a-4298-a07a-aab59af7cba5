# Mimi ni Maji - Professional Water Management App

A professional Flutter application for water management designed for students, customers, and users. Built with Supabase backend integration and modern UI/UX principles.

**"Mimi ni Maji"** means "I am Water" in Swahili, reflecting our commitment to water stewardship and management.

## Features

- **Professional Authentication System**
  - Email/Password authentication
  - Google Sign-In integration
  - GitHub Sign-In (coming soon)
  - User role selection (Student, Customer, User)

- **Modern UI/UX**
  - Professional water-themed design
  - Responsive layout
  - Material Design 3
  - Custom color scheme and typography

- **Water Management Dashboard**
  - Usage monitoring
  - Analytics and reports
  - User profile management

## Setup Instructions

### 1. Prerequisites
- Flutter SDK (3.7.2 or higher)
- Dart SDK
- Android Studio / VS Code
- Supabase account

### 2. Supabase Setup
1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your URL and anon key
3. Update `lib/main.dart` with your Supabase credentials:
   ```dart
   await Supa<PERSON>.initialize(
     url: 'YOUR_SUPABASE_URL',
     anon<PERSON><PERSON>: 'YOUR_SUPABASE_ANON_KEY',
   );
   ```

### 3. Database Schema
Run the complete database schema from the `database_schema.sql` file in your Supabase SQL editor. This includes:

- **User Profiles** - Extended user information
- **Water Sources** - Wells, reservoirs, municipal supplies
- **Usage Records** - Daily water consumption tracking
- **Quality Tests** - Water quality monitoring
- **Expenses** - Water-related costs and bills
- **Conservation Goals** - Water saving targets
- **Alerts** - System notifications
- **User Settings** - Preferences and configuration

The schema includes proper relationships, security policies, and performance indexes.

### 4. Google Sign-In Setup (Optional)
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing one
3. Enable Google Sign-In API
4. Configure OAuth consent screen
5. Create OAuth 2.0 credentials
6. Add the credentials to your Supabase project

### 5. Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```
3. Run the app:
   ```bash
   flutter run
   ```

## 🌍 **About the Name**

**"Mimi ni Maji"** is Swahili for "I am Water" - representing our deep connection to water as a vital resource. This name reflects:
- **Personal Connection**: Water is part of who we are
- **Responsibility**: We are stewards of this precious resource
- **Unity**: Water connects all life on Earth
- **Sustainability**: Managing water for future generations

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── app.dart                  # Main app configuration
├── core/
│   ├── constants/           # App constants (colors, strings, themes)
│   ├── services/            # Backend services
│   └── utils/               # Utility functions
├── features/
│   ├── auth/               # Authentication feature
│   │   ├── models/         # User models
│   │   ├── providers/      # State management
│   │   ├── screens/        # Auth screens
│   │   └── widgets/        # Auth widgets
│   └── dashboard/          # Dashboard feature
└── shared/
    └── widgets/            # Reusable widgets
```

## Technologies Used

- **Flutter** - Cross-platform mobile framework
- **Supabase** - Backend as a Service
- **Provider** - State management
- **GoRouter** - Navigation
- **Google Fonts** - Typography
- **Google Sign-In** - Social authentication

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
